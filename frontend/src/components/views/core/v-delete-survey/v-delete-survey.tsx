import { Component, Prop, State, Listen, h } from '@stencil/core';
import { deleteSurveyApi } from './helpers';
import { GetSurveyApi } from '../../../../global/script/helpers';
import { Store } from '../../../../global/script/store';
import { Router } from '../../../../';

@Component({
  tag: 'v-delete-survey',
  styleUrl: 'v-delete-survey.css',
  shadow: true,
})
export class VDeleteSurvey {
  @Prop() surveyId: string;
  @State() isDeletingSurvey: boolean = false;
  @State() surveyTitle: string = '';
  @State() isLoading: boolean = true;
  @State() loadError: boolean = false;

  @Listen('buttonClickEvent') async handleButtonClickEvent(e) {
    if (e.detail.action === 'deleteSurvey') {
      this.deleteSurvey();
    }
  }

  async deleteSurvey() {
    this.isDeletingSurvey = true;
    let { success, message } = await deleteSurveyApi(this.surveyId);
    this.isDeletingSurvey = false;
    alert(message);
    if (!success) {
      return;
    }
    // Navigate back to home page after successful deletion
    Router.push('/home');
  }

  async getSurvey() {
    this.isLoading = true;
    let { success, message, payload } = await GetSurveyApi(this.surveyId);
    this.isLoading = false;

    if (!success) {
      this.loadError = true;
      return alert(message);
    }

    if (payload && payload.title) {
      this.surveyTitle = payload.title;
    }
  }

  componentWillLoad() {
    Store.activeView = 'deleteSurvey';
    document.title = 'Delete Survey | Sensefolks';
    this.getSurvey();
  }

  render() {
    if (this.isLoading) {
      return (
        <c-main>
          <div class="container container__spinner">
            <e-spinner theme="dark"></e-spinner>
          </div>
        </c-main>
      );
    }

    if (this.loadError) {
      return (
        <c-main>
          <div class="container">
            <e-text variant="display">Error</e-text>
            <l-spacer value={1}></l-spacer>
            <e-text>Could not load survey details. Please try again later.</e-text>
            <l-spacer value={2}></l-spacer>
            <e-link url={`/surveys/${this.surveyId}/overview`}>← Back</e-link>
          </div>
        </c-main>
      );
    }

    return (
      <c-main>
        <div class="container">
          <l-row>
            <e-link url={`/surveys/${this.surveyId}/overview`}>← Back</e-link>
            <div></div>
          </l-row>
          <l-spacer value={2}></l-spacer>
          <l-row justifyContent="flex-start">
            <e-image src="../../../assets/icon/dark/trash-dark.svg" width="2em"></e-image>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="display">Delete Survey?</e-text>
          </l-row>
          <l-spacer value={1}></l-spacer>
          {/* {this.surveyTitle && (
            <div>
              <l-separator></l-separator>
              <l-spacer value={1}></l-spacer>
              <e-text variant="footnote">SURVEY TITLE</e-text>
              <l-spacer value={0.5}></l-spacer>
              <e-text variant="heading">{this.surveyTitle}</e-text>
              <l-spacer value={1}></l-spacer>
              <l-separator></l-separator>
              <l-spacer value={1}></l-spacer>
            </div>
          )} */}
          <l-separator></l-separator>
          <l-spacer value={2}></l-spacer>
          <e-text>
            Deleting your survey is an irreversible step. All survey data will be{' '}
            <u>lost forever</u>.
          </e-text>
          <l-spacer value={1}></l-spacer>
          <e-text>
            <strong>Proceed with deletion?</strong>
          </e-text>
          <l-spacer value={3}></l-spacer>
          <l-row justifyContent="flex-end" align="center">
            <e-button action="deleteSurvey" theme="danger" active={this.isDeletingSurvey}>
              Yes, delete this survey
            </e-button>
          </l-row>
        </div>
      </c-main>
    );
  }
}
