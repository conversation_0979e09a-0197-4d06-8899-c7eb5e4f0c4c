import { createSurveyPayloadInterface } from '../../interfaces';

export const generateCreateSurveyPayload = (
  type: string,
  title: string,
  distribution: string,
  embedUrl: string,
  tags: string[],
  config: object,
  respondentDetails: Array<{
    label: string;
    value: string;
    inputType: string;
    required?: boolean;
    placeholder?: string;
    options?: Array<{
      value: string;
      label: string;
    }>;
    defaultValue?: any;
  }>,
) => {
  let payload: createSurveyPayloadInterface = {
    type: type.trim(),
    title: title.trim(),
    distribution: distribution.trim(),
    embedUrl: embedUrl.trim(),
    tags: tags,
    config: config,
    respondentDetails: respondentDetails,
  };

  return payload;
};
