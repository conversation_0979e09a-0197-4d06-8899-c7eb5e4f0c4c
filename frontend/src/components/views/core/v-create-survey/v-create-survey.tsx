import { Component, State, FunctionalComponent, Listen, Host, h } from '@stencil/core';
import { createSurveyPayloadInterface } from './interfaces';
import { createSurveyApi } from './helpers';
import {
  generateCreateSurveyPayload,
  generateSensePollConfig,
  generateSenseQueryConfig,
  generateSensePriorityConfig,
  validateCreateSurveyPayload,
} from './helpers';
import { ValidateUrlPayload } from '../../../../global/script/helpers';
import { RespondentDetailsOptions, FrontendLogger } from '../../../../global/script/var';
import { Router } from '../../../..';

interface SurveyRadioProps {
  label: string;
  description: string;
  value: string;
  color: string;
}

@Component({
  tag: 'v-create-survey',
  styleUrl: 'v-create-survey.css',
  shadow: true,
})
export class VCreateSurvey {
  /* Survey Meta States */
  @State() currentStepIndex: number = 0;
  @State() isCreatingSurvey: boolean = false;
  @State() surveyConfig: any;
  @State() isCustomDetailModalOpen: boolean = false;
  @State() isEditMode: boolean = false;
  @State() editingDetail: any = null;
  @State() editingIndex: number = -1;
  @State() resetRespondentDetailsSelectTrigger: number = 0;
  @State()
  surveySteps: any = [
    { label: 'Choose Survey Type' },
    { label: 'Basic Information' },
    { label: 'Configuration (1)' },
    { label: 'Configuration (2)' },
    { label: 'Respondent Details' },
    { label: 'Thank You Message' },
    { label: 'Summary' },
  ];

  /* Survey Basics States */
  @State() surveyType: string = '';
  @State() surveyTitle: string = '';
  @State() surveyDistributionMode: string = '';
  @State() isEmbedMode: boolean = false;
  @State() surveyEmbedUrl: string = '';
  @State() surveyTags: string[] = [];
  @State() surveyThankYouMessage: string = 'Thank you for your response!';

  /* SensePoll Config States */
  @State() sensePollQuestion: string = '';
  @State() sensePollChoiceType: string = '';
  @State() sensePollChoices: { label: string; value: string }[] = [];
  @State() sensePollFollowUpChoices: any = [];
  @State() sensePollFollowUpQuestion: string = '';
  @State() isPollChoiceModalOpen: boolean = false;

  /* SenseQuery Config States */
  @State() senseQueryQuestion: string = '';
  @State() senseQueryCategories: { label: string; value: string }[] = [];
  @State() senseQueryCategory: string = '';
  @State() isCategoryModalOpen: boolean = false;
  @State() isCategoryInfoModalOpen: boolean = false;

  /* SensePriority Config States */
  @State() sensePriorityQuestion: string = '';
  @State() sensePriorityItems: { title: string; description?: string; value: string }[] = [];
  @State() sensePriorityMaxPriorities: number = 3;
  @State() isPriorityItemModalOpen: boolean = false;
  @State() isPriorityItemInfoModalOpen: boolean = false;
  @State() isEditingPriorityItem: boolean = false;
  @State() editingPriorityItem: { title: string; description?: string; value: string } | null =
    null;
  @State() editingPriorityItemIndex: number = -1;

  /* Survey Respondent States */
  @State() surveyRespondentDetails: Array<{
    label: string;
    value: string;
    inputType: string;
    placeholder?: string;
    options?: Array<{ label: string; value: string }>;
    required?: boolean;
    defaultValue?: string;
  }> = [];

  private surveyTitlePlaceholder = {
    sensePrice: 'e.g. Price Survey for Berrito Fizz',
    senseChoice: 'e.g. Feature Survey for EVStation Map',
    sensePoll: 'e.g. Payment Experience Poll',
    senseQuery: 'e.g. Landing Page FAQ Feedback',
    sensePriority: 'e.g. Feature Priority Survey',
  };

  private surveyEmbedPlaceholder = {
    sensePrice: 'e.g. https://example.com or https://example.com/pricing',
    senseChoice: 'e.g. https://example.com or https://example.com/charger-map',
    sensePoll: 'e.g. https://example.com or https://example.com/confirmation',
    senseQuery: 'e.g. https://example.com or https://example.com/help',
    sensePriority: 'e.g. https://example.com or https://example.com/features',
  };

  // Use the global RespondentDetailsOptions
  private respondentDetails = RespondentDetailsOptions;

  @Listen('buttonClickEvent') async handleButtonClickEvent(e) {
    if (e.detail.action === 'closeWizard') {
      Router.push('/');
    } else if (e.detail.action === 'nextStep') {
      if (this.currentStepIndex + 1 >= this.surveySteps.length) {
        return;
      }

      // Special logic for SenseQuery and SensePriority: skip Configuration (2) step
      if (
        (this.surveyType === 'senseQuery' || this.surveyType === 'sensePriority') &&
        this.currentStepIndex === 2
      ) {
        // Skip step 3 (Configuration 2) and go directly to step 4 (Respondent Details)
        this.currentStepIndex = this.currentStepIndex + 2;
      } else {
        this.currentStepIndex = this.currentStepIndex + 1;
      }
    } else if (e.detail.action === 'prevStep') {
      if (this.currentStepIndex <= 0) {
        return;
      }

      // Special logic for SenseQuery and SensePriority: skip Configuration (2) step when going back
      if (
        (this.surveyType === 'senseQuery' || this.surveyType === 'sensePriority') &&
        this.currentStepIndex === 4
      ) {
        // Skip step 3 (Configuration 2) and go back to step 2 (Configuration 1)
        this.currentStepIndex = this.currentStepIndex - 2;
      } else {
        this.currentStepIndex = this.currentStepIndex - 1;
      }
    } else if (e.detail.action === 'createSurvey') {
      this.createSurvey();
    } else if (e.detail.action === 'addSensePollChoice') {
      this.isPollChoiceModalOpen = true;
    } else if (e.detail.action === 'addSenseQueryCategory') {
      this.isCategoryModalOpen = true;
    } else if (e.detail.action === 'showCategoryInfo') {
      this.isCategoryInfoModalOpen = true;
    } else if (e.detail.action === 'closeCategoryInfo') {
      this.isCategoryInfoModalOpen = false;
    } else if (e.detail.action === 'showPriorityItemInfo') {
      this.isPriorityItemInfoModalOpen = true;
    } else if (e.detail.action === 'closePriorityItemInfo') {
      this.isPriorityItemInfoModalOpen = false;
    } else if (e.detail.action === 'addSensePriorityItem') {
      this.isEditingPriorityItem = false;
      this.editingPriorityItem = null;
      this.editingPriorityItemIndex = -1;
      this.isPriorityItemModalOpen = true;
    } else if (e.detail.action.startsWith('deleteRespondentDetail-')) {
      const index = parseInt(e.detail.action.split('-')[1]);
      if (index >= 0 && index < this.surveyRespondentDetails.length) {
        let updatedDetails = [];
        for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
          if (i !== index) {
            updatedDetails.push(this.surveyRespondentDetails[i]);
          }
        }
        this.surveyRespondentDetails = updatedDetails;
      }
    } else if (e.detail.action.startsWith('editRespondentDetail-')) {
      const index = parseInt(e.detail.action.split('-')[1]);
      if (index >= 0 && index < this.surveyRespondentDetails.length) {
        this.editingDetail = this.surveyRespondentDetails[index];
        this.editingIndex = index;
        this.isEditMode = true;
        this.isCustomDetailModalOpen = true;
      }
    }
  }

  @Listen('listWithDeleteEvent') async listWithDeleteEvent(e) {
    if (e.detail.name === 'deleteSensePollChoice') {
      let buff = [];
      for (let i = 0; i < this.sensePollChoices.length; i++) {
        if (this.sensePollChoices[i].value !== e.detail.value) {
          buff.push(this.sensePollChoices[i]);
        }
      }
      this.sensePollChoices = buff;
      this.sensePollChoices = [...this.sensePollChoices];

      if (this.sensePollChoices.length === 0) {
        this.sensePollFollowUpChoices = [];
        this.sensePollFollowUpChoices = [...this.sensePollFollowUpChoices];
        this.sensePollFollowUpQuestion = '';
      }
    } else if (e.detail.name === 'deleteSenseQueryCategory') {
      let buff = [];
      for (let i = 0; i < this.senseQueryCategories.length; i++) {
        if (this.senseQueryCategories[i].value !== e.detail.value) {
          buff.push(this.senseQueryCategories[i]);
        }
      }
      this.senseQueryCategories = buff;
      this.senseQueryCategories = [...this.senseQueryCategories];
    } else if (e.detail.name === 'deleteSensePriorityItem') {
      let buff = [];
      for (let i = 0; i < this.sensePriorityItems.length; i++) {
        if (this.sensePriorityItems[i].value !== e.detail.value) {
          buff.push(this.sensePriorityItems[i]);
        }
      }
      this.sensePriorityItems = buff;
      this.sensePriorityItems = [...this.sensePriorityItems];
    } else if (e.detail.name === 'deleteRespondentDetails') {
      let buff = [];
      for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
        if (this.surveyRespondentDetails[i].value !== e.detail.value) {
          buff.push(this.surveyRespondentDetails[i]);
        }
      }
      this.surveyRespondentDetails = buff;
      this.surveyRespondentDetails = [...this.surveyRespondentDetails];
    }
  }

  @Listen('inputEvent') handleInputEvent(e) {
    if (e.detail.name === 'surveyTitle') {
      this.surveyTitle = e.detail.value;
    } else if (e.detail.name === 'surveyEmbedUrl') {
      this.surveyEmbedUrl = e.detail.value;
    } else if (e.detail.name === 'surveyDistributionMode') {
      this.surveyDistributionMode = e.detail.value;
      if (this.surveyDistributionMode === 'link') {
        this.surveyEmbedUrl = '';
      }
      this.handleCaptureWebpageUrl(this.surveyDistributionMode);
    } else if (e.detail.name === 'sensePollChoiceType') {
      this.sensePollChoiceType = e.detail.value;
    } else if (e.detail.name === 'sensePollQuestion') {
      this.sensePollQuestion = e.detail.value;
    } else if (e.detail.name === 'sensePollFollowUpChoice') {
      if (e.detail.isChecked) {
        let obj = {
          label: e.detail.label,
          value: e.detail.value,
        };
        this.sensePollFollowUpChoices.push(obj);
        this.sensePollFollowUpChoices = [...this.sensePollFollowUpChoices];
      } else {
        let buff = [];
        for (let i = 0; i < this.sensePollFollowUpChoices.length; i++) {
          if (this.sensePollFollowUpChoices[i].value !== e.detail.value) {
            buff.push(this.sensePollFollowUpChoices[i]);
          }
        }
        this.sensePollFollowUpChoices = buff;
        this.sensePollFollowUpChoices = [...this.sensePollFollowUpChoices];

        if (this.sensePollFollowUpChoices.length === 0) {
          this.sensePollFollowUpQuestion = '';
        }
      }
    } else if (e.detail.name === 'sensePollFollowUpQuestion') {
      this.sensePollFollowUpQuestion = e.detail.value;
    } else if (e.detail.name === 'senseQueryQuestion') {
      this.senseQueryQuestion = e.detail.value;
    } else if (e.detail.name === 'senseQueryCategory') {
      this.senseQueryCategory = e.detail.value;
    } else if (e.detail.name === 'sensePriorityQuestion') {
      this.sensePriorityQuestion = e.detail.value;
    } else if (e.detail.name === 'sensePriorityMaxPriorities') {
      this.sensePriorityMaxPriorities = parseInt(e.detail.value) || 3;
    } else if (e.detail.name === 'surveyThankYouMessage') {
      this.surveyThankYouMessage = e.detail.value;
    }
  }

  @Listen('addCustomRespondentDetail') handleAddCustomRespondentDetail(e) {
    FrontendLogger.debug('Received addCustomRespondentDetail:', e.detail.respondentDetail);
    const newDetail = e.detail.respondentDetail;
    const isEditMode = this.isEditMode;

    if (isEditMode && this.editingIndex >= 0) {
      // Update existing detail
      this.surveyRespondentDetails[this.editingIndex] = newDetail;
      this.surveyRespondentDetails = [...this.surveyRespondentDetails];
    } else {
      // Check if the respondent detail already exists in the array
      let isDuplicate = false;
      for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
        if (this.surveyRespondentDetails[i].value === newDetail.value) {
          isDuplicate = true;
          break;
        }
      }

      // Only add if it's not a duplicate
      if (isDuplicate) {
        return;
      }

      // Add the new detail to the array
      this.surveyRespondentDetails = [...this.surveyRespondentDetails, newDetail];
    }

    // Close the modal and reset edit state
    this.isCustomDetailModalOpen = false;
    this.isEditMode = false;
    this.editingDetail = null;
    this.editingIndex = -1;

    // Reset the dropdown to "Choose Respondent Details"
    this.resetRespondentDetailsSelectTrigger = Date.now();
  }

  @Listen('modalCloseEvent') handleModalCloseEvent() {
    // Handle respondent detail modal close
    if (this.isCustomDetailModalOpen) {
      this.isCustomDetailModalOpen = false;
      this.isEditMode = false;
      this.editingDetail = null;
      this.editingIndex = -1;

      // Reset the dropdown to "Choose Respondent Details"
      this.resetRespondentDetailsSelectTrigger = Date.now();
    }

    // Handle priority item modal close
    if (this.isPriorityItemModalOpen) {
      this.isPriorityItemModalOpen = false;
      this.isEditingPriorityItem = false;
      this.editingPriorityItem = null;
      this.editingPriorityItemIndex = -1;
    }

    // Handle category modal close
    if (this.isCategoryModalOpen) {
      this.isCategoryModalOpen = false;
      this.senseQueryCategory = '';
    }

    // Handle category info modal close
    if (this.isCategoryInfoModalOpen) {
      this.isCategoryInfoModalOpen = false;
    }

    // Handle priority item info modal close
    if (this.isPriorityItemInfoModalOpen) {
      this.isPriorityItemInfoModalOpen = false;
    }

    // Handle poll choice modal close
    if (this.isPollChoiceModalOpen) {
      this.isPollChoiceModalOpen = false;
    }
  }

  @Listen('addSenseQueryCategoryFromModal')
  handleAddSenseQueryCategoryFromModal(e: CustomEvent) {
    const categoryValue = e.detail.category;
    if (!categoryValue || !categoryValue.trim()) {
      return;
    }

    // Check for duplicates
    let isDuplicate = false;
    for (let i = 0; i < this.senseQueryCategories.length; i++) {
      if (
        this.senseQueryCategories[i].value === categoryValue ||
        this.senseQueryCategories[i].label === categoryValue
      ) {
        isDuplicate = true;
        break;
      }
    }

    if (!isDuplicate) {
      let obj = {
        label: categoryValue,
        value: categoryValue.toLowerCase().replace(/\s+/g, '-'),
      };
      this.senseQueryCategories.push(obj);
      this.senseQueryCategories = [...this.senseQueryCategories];
    }

    // Close modal and reset
    this.isCategoryModalOpen = false;
    this.senseQueryCategory = '';
  }

  @Listen('addSensePollChoiceFromModal')
  handleAddSensePollChoiceFromModal(e: CustomEvent) {
    const choiceValue = e.detail.choice;
    if (!choiceValue || !choiceValue.trim()) {
      return;
    }

    const choiceObj = {
      label: choiceValue.trim(),
      value: choiceValue.trim().toLowerCase().replace(/\s+/g, '-'),
    };

    this.sensePollChoices.push(choiceObj);
    this.sensePollChoices = [...this.sensePollChoices];

    // Close modal
    this.isPollChoiceModalOpen = false;
  }

  @Listen('respondentDetailDeleteEvent') handleRespondentDetailDeleteEvent(e) {
    const valueToDelete = e.detail.value;

    // Filter out the detail to delete
    let updatedDetails = [];
    for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
      if (this.surveyRespondentDetails[i].value !== valueToDelete) {
        updatedDetails.push(this.surveyRespondentDetails[i]);
      }
    }

    this.surveyRespondentDetails = updatedDetails;
  }

  @Listen('addCustomPriorityItem')
  handleAddCustomPriorityItem(e: CustomEvent) {
    const newItem = e.detail.priorityItem;
    const isEditMode = this.isEditingPriorityItem;

    if (isEditMode && this.editingPriorityItemIndex >= 0) {
      // Update existing item
      this.sensePriorityItems[this.editingPriorityItemIndex] = newItem;
      this.sensePriorityItems = [...this.sensePriorityItems];
    } else {
      // Check if this item already exists (for new items)
      let alreadyExists = false;
      for (let i = 0; i < this.sensePriorityItems.length; i++) {
        if (this.sensePriorityItems[i].value === newItem.value) {
          alreadyExists = true;
          break;
        }
      }

      if (!alreadyExists) {
        this.sensePriorityItems = [...this.sensePriorityItems, newItem];
      }
    }

    // Close the modal and reset edit state
    this.isPriorityItemModalOpen = false;
    this.isEditingPriorityItem = false;
    this.editingPriorityItem = null;
    this.editingPriorityItemIndex = -1;
  }

  @Listen('priorityItemDeleteEvent')
  handlePriorityItemDeleteEvent(e: CustomEvent) {
    const valueToDelete = e.detail.value;

    // Filter out the item to delete
    let updatedItems = [];
    for (let i = 0; i < this.sensePriorityItems.length; i++) {
      if (this.sensePriorityItems[i].value !== valueToDelete) {
        updatedItems.push(this.sensePriorityItems[i]);
      }
    }

    this.sensePriorityItems = updatedItems;
  }

  @Listen('priorityItemEditEvent')
  handlePriorityItemEditEvent(e: CustomEvent) {
    this.editingPriorityItem = e.detail.item;
    this.editingPriorityItemIndex = e.detail.index;
    this.isEditingPriorityItem = true;
    this.isPriorityItemModalOpen = true;
  }

  @Listen('selectChangeEvent') handleSelectChangeEvent(e) {
    if (e.detail.name === 'surveyRespondentDetails') {
      if (e.detail.value === '-') {
        return;
      }

      // If "Create Custom Detail" is selected, open the modal
      if (e.detail.value === 'custom') {
        this.isEditMode = false;
        this.editingDetail = null;
        this.editingIndex = -1;
        this.isCustomDetailModalOpen = true;
        return;
      }

      // Find the selected option in RespondentDetailsOptions to get inputType
      const selectedOption = RespondentDetailsOptions.find(
        option => option.value === e.detail.value,
      );

      let obj = {
        label: e.detail.label,
        value: e.detail.value,
        inputType: selectedOption?.inputType || 'text',
        placeholder: selectedOption?.placeholder,
        options: selectedOption?.options,
        required: selectedOption?.required || false,
        defaultValue: selectedOption?.defaultValue,
      };

      // Check if the respondent detail already exists in the array
      let isDuplicate = false;
      for (let i = 0; i < this.surveyRespondentDetails.length; i++) {
        if (this.surveyRespondentDetails[i].value === obj.value) {
          isDuplicate = true;
          break;
        }
      }

      // Only add if it's not a duplicate
      if (isDuplicate) {
        return;
      }

      this.surveyRespondentDetails.push(obj);
      this.surveyRespondentDetails = [...this.surveyRespondentDetails];

      // Reset the dropdown after adding an item
      this.resetRespondentDetailsSelectTrigger = Date.now();
    }
  }

  private handleCaptureWebpageUrl(value: string) {
    if (value === 'embed' || value === 'embed&link') {
      this.isEmbedMode = true;
    } else {
      this.isEmbedMode = false;
    }
  }

  private handleSurveyTypeSelection(value: string) {
    this.surveyType = value;
    this.resetWizard();
  }

  private generateSurveyName(value: string) {
    if (value === 'sensePrice') {
      return 'SensePrice';
    } else if (value === 'senseChoice') {
      return 'SenseChoice';
    } else if (value === 'sensePoll') {
      return 'SensePoll';
    } else if (value === 'senseQuery') {
      return 'SenseQuery';
    } else if (value === 'sensePriority') {
      return 'SensePriority';
    }
  }

  private getDisplayStepNumber() {
    // For SenseQuery and SensePriority, adjust step numbers since we skip step 3
    if (
      (this.surveyType === 'senseQuery' || this.surveyType === 'sensePriority') &&
      this.currentStepIndex >= 4
    ) {
      return this.currentStepIndex; // Step 4 becomes step 4 in display (since we skip step 3)
    }
    return this.currentStepIndex + 1;
  }

  private getDisplayTotalSteps() {
    // For SenseQuery and SensePriority, we have one fewer step since we skip Configuration (2)
    if (this.surveyType === 'senseQuery' || this.surveyType === 'sensePriority') {
      return this.surveySteps.length - 1;
    }
    return this.surveySteps.length;
  }

  async createSurvey() {
    if (this.surveyType === 'sensePoll') {
      this.surveyConfig = generateSensePollConfig(
        this.sensePollQuestion,
        this.sensePollChoiceType,
        this.sensePollChoices,
        this.sensePollFollowUpChoices,
        this.sensePollFollowUpQuestion,
        this.surveyThankYouMessage,
      );
    } else if (this.surveyType === 'senseQuery') {
      this.surveyConfig = generateSenseQueryConfig(
        this.senseQueryQuestion,
        this.senseQueryCategories,
        this.surveyThankYouMessage,
      );
    } else if (this.surveyType === 'sensePriority') {
      this.surveyConfig = generateSensePriorityConfig(
        this.sensePriorityQuestion,
        this.sensePriorityItems,
        this.surveyThankYouMessage,
      );
    }

    let createSurveyPayload: createSurveyPayloadInterface = generateCreateSurveyPayload(
      this.surveyType,
      this.surveyTitle,
      this.surveyDistributionMode,
      this.surveyEmbedUrl,
      this.surveyTags,
      this.surveyConfig,
      this.surveyRespondentDetails,
    );

    // Debug logging to see what's being validated
    FrontendLogger.debug('Survey payload before validation:', createSurveyPayload);
    FrontendLogger.debug('Respondent details:', this.surveyRespondentDetails);

    let { isValid, validationMessage } = validateCreateSurveyPayload(createSurveyPayload);

    if (!isValid) {
      return alert(validationMessage);
    }

    this.isCreatingSurvey = true;
    let { success, message } = await createSurveyApi(createSurveyPayload);
    this.isCreatingSurvey = false;
    if (!success) {
      return alert(message);
    }
    Router.push('/');
  }

  private resetWizard() {
    /* Survey Basics States */
    this.surveyTitle = '';
    this.surveyDistributionMode = '';
    this.surveyEmbedUrl = '';
    this.isEmbedMode = false;
    this.surveyTags = [];
    this.surveyTags = [...this.surveyTags];
    this.surveyConfig = {};
    this.surveyRespondentDetails = [];
    this.surveyRespondentDetails = [...this.surveyRespondentDetails];
    this.surveyThankYouMessage = 'Thank you for your response!';

    /* SensePoll States */
    this.sensePollQuestion = '';
    this.sensePollChoiceType = '';
    this.sensePollChoices = [];
    this.sensePollChoices = [...this.sensePollChoices];
    this.sensePollFollowUpChoices = [];
    this.sensePollFollowUpChoices = [...this.sensePollFollowUpChoices];
    this.isPollChoiceModalOpen = false;

    /* SenseQuery States */
    this.senseQueryQuestion = '';
    this.senseQueryCategories = [];
    this.senseQueryCategories = [...this.senseQueryCategories];
    this.senseQueryCategory = '';
    this.isCategoryModalOpen = false;

    /* SensePriority States */
    this.sensePriorityQuestion = '';
    this.sensePriorityItems = [];
    this.sensePriorityItems = [...this.sensePriorityItems];
    this.sensePriorityMaxPriorities = 3;
    this.isPriorityItemModalOpen = false;
    this.isEditingPriorityItem = false;
    this.editingPriorityItem = null;
    this.editingPriorityItemIndex = -1;

    /* Survey Respondent States */
    this.surveyRespondentDetails = [];
    this.surveyRespondentDetails = [...this.surveyRespondentDetails];
  }

  private basicStepNextButton(title: string, distribution: string, embedUrl: string) {
    let isTitleReady: boolean = false;
    let isDistributionReady: boolean = false;

    if (title.length > 0) {
      isTitleReady = true;
    } else {
      isTitleReady = false;
    }

    if (distribution === 'embed' || distribution === 'embed&link') {
      if (embedUrl.length === 0) {
        isDistributionReady = false;
      } else {
        let { isValid } = ValidateUrlPayload({ url: embedUrl });
        if (!isValid) {
          isDistributionReady = false;
        } else {
          isDistributionReady = true;
        }
      }
    } else if (distribution === 'link') {
      isDistributionReady = true;
    } else {
      isDistributionReady = false;
    }

    return !isTitleReady || !isDistributionReady;
  }

  private checkExistenceInFollowUpChoices(value: string) {
    let exists: boolean = false;

    if (this.sensePollFollowUpChoices.length === 0) {
      return false;
    }

    for (let i = 0; i < this.sensePollFollowUpChoices.length; i++) {
      if (this.sensePollFollowUpChoices[i] && this.sensePollFollowUpChoices[i].value === value) {
        exists = true;
        break;
      }
    }
    return exists;
  }

  private pollSenseConfig2NextButton(choices: any, followUpChoices: any, followUpQuestion: string) {
    let isChoicesReady: boolean = false;
    let isFollowUpReady: boolean = false;

    if (choices.length > 0) {
      isChoicesReady = true;
    } else {
      isChoicesReady = false;
    }

    if (followUpChoices.length > 0) {
      if (followUpQuestion.length > 0) {
        isFollowUpReady = true;
      } else {
        isFollowUpReady = false;
      }
    } else {
      isFollowUpReady = true;
    }

    return !isChoicesReady || !isFollowUpReady;
  }

  /* Local Components */
  SurveyRadio: FunctionalComponent<SurveyRadioProps> = ({ label, description, value, color }) => (
    <div
      class={`survey-radio ${this.surveyType === value && 'survey-radio--active'}`}
      data-color={color}
      onClick={() => this.handleSurveyTypeSelection(value)}
    >
      <e-text class={`survey-title survey-title--${color}`}>
        <strong>{label}</strong>
      </e-text>
      <l-spacer value={0.25}></l-spacer>
      <e-text variant="footnote">{description}</e-text>
    </div>
  );

  /* SenseQuery Wizard Steps */
  SenseQueryConfig1: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Enter question <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="text"
        name="senseQueryQuestion"
        placeholder="e.g. Do you have any queries about installation?"
        value={this.senseQueryQuestion}
      ></e-input>
      <l-spacer value={2.5}></l-spacer>
      <l-row justifyContent="space-between">
        <l-row justifyContent="flex-start">
          <e-text>
            <strong>Question categories</strong>
          </e-text>
          <l-spacer variant="horizontal" value={0.25}></l-spacer>
          <e-button variant="link" action="showCategoryInfo">
            <e-image src="../../../assets/icon/light/info-light.svg" width="1.2em"></e-image>
          </e-button>
        </l-row>
        <e-button variant="link" action="addSenseQueryCategory">
          + Add Category
        </e-button>
      </l-row>
      <l-spacer value={0.5}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={1}></l-spacer>
      <p-list-with-delete
        name="deleteSenseQueryCategory"
        items={JSON.stringify(this.senseQueryCategories)}
        emptyMessage="No categories added yet"
      ></p-list-with-delete>
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button disabled={this.senseQueryQuestion ? false : true} action="nextStep">
          Next
        </e-button>
      </div>
    </div>
  );

  /* SensePriority Wizard Steps */
  SensePriorityConfig1: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Enter question <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="text"
        name="sensePriorityQuestion"
        placeholder="e.g. Which features are most important to you?"
        value={this.sensePriorityQuestion}
      ></e-input>
      <l-spacer value={2.5}></l-spacer>
      <l-row justifyContent="space-between">
        <l-row justifyContent="flex-start">
          <e-text>
            <strong>
              Requirements <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <l-spacer variant="horizontal" value={0.25}></l-spacer>
          <e-button variant="link" action="showPriorityItemInfo">
            <e-image src="../../../assets/icon/light/info-light.svg" width="1.2em"></e-image>
          </e-button>
        </l-row>
        <e-button variant="link" action="addSensePriorityItem">
          + Add Requirement
        </e-button>
      </l-row>
      <l-spacer value={0.5}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={1}></l-spacer>

      {this.sensePriorityItems.length === 0 && (
        <div class="empty-state">
          <e-text variant="footnote">No requirements added yet</e-text>
        </div>
      )}

      {this.sensePriorityItems.length > 0 && (
        <div class="priority-items-list">
          {this.sensePriorityItems.map((item, index) => (
            <p-priority-item item={JSON.stringify(item)} index={index}></p-priority-item>
          ))}
        </div>
      )}

      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.sensePriorityQuestion && this.sensePriorityItems.length > 0 ? false : true}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  SenseQueryPreview: FunctionalComponent = () => (
    <c-card>
      <l-row justifyContent="flex-start">
        <e-image src="../../../assets/icon/light/gear-six-light.svg" width="2em"></e-image>
        <l-spacer variant="horizontal" value={0.5}></l-spacer>
        <e-text variant="heading">SenseQuery Settings</e-text>
      </l-row>{' '}
      <l-spacer value={1}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">QUESTION</e-text>
      <e-text>{this.senseQueryQuestion}</e-text>
      {this.senseQueryCategories.length > 0 && (
        <div>
          <l-spacer value={2}></l-spacer>
          <e-text variant="footnote">CATEGORIES</e-text>
          <l-spacer value={0.25}></l-spacer>
          <ul class="survey-preview-options-list">
            {this.senseQueryCategories.map((obj: any) => (
              <li>{obj.label}</li>
            ))}
          </ul>
        </div>
      )}
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">THANK YOU MESSAGE</e-text>
      <e-text>{this.surveyThankYouMessage}</e-text>
    </c-card>
  );

  SensePriorityPreview: FunctionalComponent = () => (
    <c-card>
      <l-row justifyContent="flex-start">
        <e-image src="../../../assets/icon/light/gear-six-light.svg" width="2em"></e-image>
        <l-spacer variant="horizontal" value={0.5}></l-spacer>
        <e-text variant="heading">SensePriority Settings</e-text>
      </l-row>
      <l-spacer value={1}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">QUESTION</e-text>
      <e-text>{this.sensePriorityQuestion}</e-text>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">MAX PRIORITIES</e-text>
      <e-text>{this.sensePriorityMaxPriorities}</e-text>
      {this.sensePriorityItems.length > 0 && (
        <div>
          <l-spacer value={2}></l-spacer>
          <e-text variant="footnote">PRIORITY ITEMS</e-text>
          <l-spacer value={0.25}></l-spacer>
          <ul class="survey-preview-options-list">
            {this.sensePriorityItems.map((item: any) => (
              <li>
                <strong>{item.title}</strong>
                {item.description && (
                  <div style={{ fontSize: '0.9em', color: '#666', marginTop: '0.25em' }}>
                    {item.description}
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">THANK YOU MESSAGE</e-text>
      <e-text>{this.surveyThankYouMessage}</e-text>
    </c-card>
  );

  /* SensePoll Wizard Steps */
  SensePollConfig1: FunctionalComponent = () => (
    <div>
      <e-text>
        <strong>
          Enter poll question <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="text"
        name="sensePollQuestion"
        placeholder="e.g. How was the checkout experience?"
        value={this.sensePollQuestion}
      ></e-input>
      <l-spacer value={2.5}></l-spacer>
      <e-text>
        <strong>
          How many answers can respondents select? <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <l-row justifyContent="flex-start">
        <e-input
          type="radio"
          name="sensePollChoiceType"
          value="singleChoice"
          checked={this.sensePollChoiceType === 'singleChoice' ? true : false}
        >
          Single Answer
        </e-input>
        <l-spacer variant="horizontal" value={1}></l-spacer>
        <e-input
          type="radio"
          name="sensePollChoiceType"
          value="multiChoice"
          checked={this.sensePollChoiceType === 'multiChoice' ? true : false}
        >
          Multi Answers
        </e-input>
      </l-row>
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.sensePollQuestion && this.sensePollChoiceType ? false : true}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  SensePollConfig2: FunctionalComponent = () => (
    <div>
      <div class="adjustment-row">
        <e-text>
          <strong>
            Poll Answers <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <e-button variant="link" action="addSensePollChoice">
          + Add Answer
        </e-button>
      </div>
      <l-spacer value={0.5}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={1}></l-spacer>
      <p-list-with-delete
        name="deleteSensePollChoice"
        items={JSON.stringify(this.sensePollChoices)}
        emptyMessage="No answers added yet"
      ></p-list-with-delete>
      <l-spacer value={2.5}></l-spacer>
      {this.sensePollChoices.length > 0 && (
        <div>
          <l-spacer value={2.5}></l-spacer>
          <e-text>
            <strong>Ask follow-up question for the following answers</strong> (Optional)
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <div class="gallery">
            {this.sensePollChoices.map((option: any) => (
              <e-input
                type="checkbox"
                value={option.value}
                name="sensePollFollowUpChoice"
                checked={this.checkExistenceInFollowUpChoices(option.value)}
                label={option.label}
              >
                {option.label}
              </e-input>
            ))}
          </div>
        </div>
      )}
      {this.sensePollFollowUpChoices.length > 0 && this.sensePollChoices.length > 0 && (
        <div>
          <l-spacer value={2.5}></l-spacer>
          <e-text>
            <strong>Enter follow-up question</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="sensePollFollowUpQuestion"
            placeholder="What would you like to know further?"
            value={this.sensePollFollowUpQuestion}
          ></e-input>
        </div>
      )}
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.pollSenseConfig2NextButton(
            this.sensePollChoices,
            this.sensePollFollowUpChoices,
            this.sensePollFollowUpQuestion,
          )}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </div>
  );

  SensePollPreview: FunctionalComponent = () => (
    <c-card>
      <l-row justifyContent="flex-start">
        <e-image src="../../../assets/icon/light/gear-six-light.svg" width="2em"></e-image>
        <l-spacer variant="horizontal" value={0.5}></l-spacer>
        <e-text variant="heading">SensePoll Settings</e-text>
      </l-row>{' '}
      <l-spacer value={1}></l-spacer>
      <l-separator></l-separator>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">POLL QUESTION</e-text>
      <e-text>{this.sensePollQuestion}</e-text>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">ANSWER CHOICE TYPE</e-text>
      <e-text>
        {this.sensePollChoiceType === 'multiChoice' ? 'Multiple Choice' : 'Single Choice'}
      </e-text>
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">ANSWER OPTIONS</e-text>
      <l-spacer value={0.25}></l-spacer>
      <ul class="survey-preview-options-list">
        {this.sensePollChoices.map((obj: any) => (
          <li>{obj.label}</li>
        ))}
      </ul>
      {this.sensePollFollowUpChoices.length > 0 && this.sensePollChoices.length > 0 && (
        <div>
          <l-spacer value={2}></l-spacer>
          <e-text variant="footnote">ASK ADDITIONAL DETAILS WHEN:</e-text>
          <l-spacer value={0.25}></l-spacer>
          <ul class="survey-preview-options-list">
            {this.sensePollFollowUpChoices.map((obj: any) => (
              <li>"{obj.label}" is selected</li>
            ))}
          </ul>
        </div>
      )}
      <l-spacer value={2}></l-spacer>
      <e-text variant="footnote">THANK YOU MESSAGE</e-text>
      <e-text>{this.surveyThankYouMessage}</e-text>
    </c-card>
  );

  /* Core Wizard Steps */
  SurveyTypeStep: FunctionalComponent = () => (
    <article>
      <this.SurveyRadio
        label="SensePrice"
        description="Find the optimal price your customers are willing to pay"
        value="sensePrice"
        color="purple"
      ></this.SurveyRadio>
      <this.SurveyRadio
        label="SenseChoice"
        description="Understand what truly matters to your customers"
        value="senseChoice"
        color="blue"
      ></this.SurveyRadio>
      <this.SurveyRadio
        label="SensePoll"
        description="Capture quick customer opinions and preferences"
        value="sensePoll"
        color="indigo"
      ></this.SurveyRadio>
      <this.SurveyRadio
        label="SenseQuery"
        description="Discover the questions your customers are asking"
        value="senseQuery"
        color="turquoise"
      ></this.SurveyRadio>
      <this.SurveyRadio
        label="SensePriority"
        description="Identify & prioritize customer requirements"
        value="sensePriority"
        color="teal"
      ></this.SurveyRadio>
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <div></div>
        <e-button disabled={this.surveyType.length > 0 ? false : true} action="nextStep">
          Next
        </e-button>
      </div>
    </article>
  );

  SurveyBasicsStep: FunctionalComponent = () => (
    <article>
      <e-text>
        <strong>
          Survey Title <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="text"
        name="surveyTitle"
        placeholder={this.surveyTitlePlaceholder[this.surveyType]}
        value={this.surveyTitle}
      ></e-input>
      <l-spacer value={0.5}></l-spacer>
      <e-text variant="footnote">
        <e-link variant="externalLink" url="https://sensefolks.com">
          Read survey naming guide (1 min)
        </e-link>
      </e-text>
      <l-spacer value={2.5}></l-spacer>
      <e-text>
        <strong>
          How will you distribute this survey? <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-input
        type="radio"
        name="surveyDistributionMode"
        value="embed"
        checked={this.surveyDistributionMode === 'embed' ? true : false}
      >
        Embed on a website
      </e-input>
      <l-spacer value={1}></l-spacer>
      <e-input
        type="radio"
        name="surveyDistributionMode"
        value="link"
        checked={this.surveyDistributionMode === 'link' ? true : false}
      >
        Share as a link{' '}
      </e-input>
      <l-spacer value={1}></l-spacer>
      <e-input
        type="radio"
        name="surveyDistributionMode"
        value="embed&link"
        checked={this.surveyDistributionMode === 'embed&link' ? true : false}
      >
        Both embed & share
      </e-input>
      {this.isEmbedMode && (
        <div>
          <l-spacer value={2.5}></l-spacer>
          <e-text>
            <strong>
              2.1. Enter URL where survey will be embedded <span class="mandatory"> * </span>
            </strong>
          </e-text>
          <e-text variant="footnote">IMPORTANT: Only secure URLs are supported</e-text>
          <l-spacer value={0.5}></l-spacer>
          <e-input
            type="text"
            name="surveyEmbedUrl"
            placeholder={this.surveyEmbedPlaceholder[this.surveyType]}
            value={this.surveyEmbedUrl}
          ></e-input>
        </div>
      )}
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.basicStepNextButton(
            this.surveyTitle,
            this.surveyDistributionMode,
            this.surveyEmbedUrl,
          )}
          action="nextStep"
        >
          Next
        </e-button>
      </div>
    </article>
  );

  SurveyConfigPart1Step: FunctionalComponent = () => (
    <article>
      {this.surveyType === 'sensePoll' && <this.SensePollConfig1></this.SensePollConfig1>}
      {this.surveyType === 'senseQuery' && <this.SenseQueryConfig1></this.SenseQueryConfig1>}
      {this.surveyType === 'sensePriority' && (
        <this.SensePriorityConfig1></this.SensePriorityConfig1>
      )}

      {/* Modal for priority item - SensePriority specific */}
      {this.surveyType === 'sensePriority' && (
        <p-modal
          is-open={this.isPriorityItemModalOpen}
          modal-title={this.isEditingPriorityItem ? 'Edit Requirement' : 'Add Requirement'}
        >
          {this.isPriorityItemModalOpen && (
            <p-priority-item-form
              editing-item={
                this.editingPriorityItem ? JSON.stringify(this.editingPriorityItem) : ''
              }
              is-edit-mode={this.isEditingPriorityItem}
            ></p-priority-item-form>
          )}
        </p-modal>
      )}

      {/* Modal for category - SenseQuery specific */}
      {this.surveyType === 'senseQuery' && (
        <p-modal is-open={this.isCategoryModalOpen} modal-title="Add Category">
          {this.isCategoryModalOpen && <p-category-form></p-category-form>}
        </p-modal>
      )}

      {/* Modal for category information - SenseQuery specific */}
      {this.surveyType === 'senseQuery' && (
        <p-modal is-open={this.isCategoryInfoModalOpen} modal-title="Benefits of Categories">
          {this.isCategoryInfoModalOpen && (
            <div>
              <e-text>
                Respondents can choose a category that is relevant to their query e.g. Pricing,
                General, Support etc. This helps you to:
              </e-text>
              <l-spacer value={1}></l-spacer>
              <ul style={{ paddingLeft: '1.5em', margin: '0' }}>
                <li>
                  <e-text>
                    <strong>Group similar queries</strong> for easier analysis
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Spot unanswered topics</strong> in categories
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Identify areas</strong> that need most attention
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Present findings</strong> to different teams based on the categories
                  </e-text>
                </li>
              </ul>
              <l-spacer value={2}></l-spacer>
              <l-row justifyContent="space-between" direction="row-reverse">
                <e-button action="closeCategoryInfo">Ok</e-button>
              </l-row>
            </div>
          )}
        </p-modal>
      )}

      {/* Modal for priority items information - SensePriority specific */}
      {this.surveyType === 'sensePriority' && (
        <p-modal is-open={this.isPriorityItemInfoModalOpen} modal-title="About Requirements">
          {this.isPriorityItemInfoModalOpen && (
            <div>
              <e-text>
                Requirments represent features or capabilites of a product needed by users e.g.
                search feature, dark mode, etc.
              </e-text>
              <l-spacer value={1}></l-spacer>
              <e-text>Prioritizing requirements help you to:</e-text>
              <l-spacer value={1}></l-spacer>
              <ul style={{ paddingLeft: '1.5em', margin: '0' }}>
                <li>
                  <e-text>
                    <strong>Understand user priorities</strong> and what matters most to them
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Make data-driven decisions</strong> about feature development
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Allocate resources</strong> to the most important requirements
                  </e-text>
                </li>
                <l-spacer value={0.5}></l-spacer>
                <li>
                  <e-text>
                    <strong>Validate assumptions</strong> about what users want most
                  </e-text>
                </li>
              </ul>
              <l-spacer value={2}></l-spacer>
              <l-row justifyContent="space-between" direction="row-reverse">
                <e-button action="closePriorityItemInfo">Ok</e-button>
              </l-row>
            </div>
          )}
        </p-modal>
      )}
    </article>
  );

  SurveyConfigPart2Step: FunctionalComponent = () => (
    <article>
      {this.surveyType === 'sensePoll' && <this.SensePollConfig2></this.SensePollConfig2>}
      {this.surveyType === 'senseQuery' && (
        <div>
          <e-text>
            <strong>SenseQuery configuration is complete!</strong>
          </e-text>
          <l-spacer value={1}></l-spacer>
          <e-text variant="footnote">
            Your query survey has been configured. Click Next to continue with respondent details.
          </e-text>
          <l-spacer value={3}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={1.5}></l-spacer>
          <div class="row">
            <e-button variant="light" action="prevStep">
              Back
            </e-button>
            <e-button action="nextStep">Next</e-button>
          </div>
        </div>
      )}
      {this.surveyType === 'sensePriority' && (
        <div>
          <e-text>
            <strong>SensePriority configuration is complete!</strong>
          </e-text>
          <l-spacer value={1}></l-spacer>
          <e-text variant="footnote">
            Your priority survey has been configured. Click Next to continue with respondent
            details.
          </e-text>
          <l-spacer value={3}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={1.5}></l-spacer>
          <div class="row">
            <e-button variant="light" action="prevStep">
              Back
            </e-button>
            <e-button action="nextStep">Next</e-button>
          </div>
        </div>
      )}

      {/* Modal for poll choice - SensePoll specific */}
      {this.surveyType === 'sensePoll' && (
        <p-modal is-open={this.isPollChoiceModalOpen} modal-title="Add Answer">
          {this.isPollChoiceModalOpen && <p-poll-answer-form></p-poll-answer-form>}
        </p-modal>
      )}
    </article>
  );

  SurveyRespondentDetailsStep: FunctionalComponent = () => (
    <article>
      <e-text>
        <strong>What details do you want to know about the respondents?</strong> (Optional)
      </e-text>
      <l-spacer value={1}></l-spacer>

      {/* Dropdown selection with Create Custom Detail option */}
      <e-select
        name="surveyRespondentDetails"
        options={JSON.stringify(this.respondentDetails)}
        resetTrigger={this.resetRespondentDetailsSelectTrigger}
      ></e-select>
      <l-spacer value={2}></l-spacer>

      {/* Display selected respondent details */}
      {this.surveyRespondentDetails.length > 0 && (
        <div>
          <e-text variant="footnote">
            The following details will be collected from respondents
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          {this.surveyRespondentDetails.map((detail, index) => (
            <div>
              {index > 0 && <l-spacer value={2}></l-spacer>}
              <div class="respondent-detail-container">
                <c-card>
                  <l-row justifyContent="space-between" align="center">
                    <e-text>
                      <strong>{detail.label}</strong>
                      {detail.required && <span class="required-badge"> * </span>}
                    </e-text>
                    <div class="detail-actions">
                      <e-button variant="link" action={`editRespondentDetail-${index}`}>
                        <e-image
                          src="../../../assets/icon/dark/edit-dark.svg"
                          width="1.2em"
                        ></e-image>
                      </e-button>
                      <e-button variant="link" action={`deleteRespondentDetail-${index}`}>
                        <e-image
                          src="../../../assets/icon/red/trash-red.svg"
                          width="1.2em"
                        ></e-image>
                      </e-button>
                    </div>
                  </l-row>
                  <l-spacer value={2}></l-spacer>
                  <div class="detail-content">
                    <div class="detail-section">
                      <e-text variant="footnote" class="detail-label">
                        TYPE
                      </e-text>
                      <e-text>{detail.inputType}</e-text>
                    </div>

                    {detail.placeholder && (
                      <div>
                        <l-spacer value={2}></l-spacer>
                        <div class="detail-section">
                          <e-text variant="footnote" class="detail-label">
                            PLACEHOLDER
                          </e-text>
                          <e-text>{detail.placeholder}</e-text>
                        </div>
                      </div>
                    )}

                    {detail.options && detail.options.length > 0 && (
                      <div>
                        <l-spacer value={2}></l-spacer>
                        <div class="detail-section">
                          <e-text variant="footnote" class="detail-label">
                            OPTIONS
                          </e-text>
                          <ul class="options-list">
                            {detail.options.map(option => (
                              <li class="option-item">
                                <e-text>{option.label}</e-text>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}

                    {detail.defaultValue && (
                      <div>
                        <l-spacer value={2}></l-spacer>
                        <div class="detail-section">
                          <e-text variant="footnote" class="detail-label">
                            DEFAULT VALUE
                          </e-text>
                          <e-text>{detail.defaultValue}</e-text>
                        </div>
                      </div>
                    )}
                  </div>
                </c-card>
              </div>
            </div>
          ))}
          <l-spacer value={1}></l-spacer>
          <e-text variant="footnote">
            <span class="required-badge"> * </span> are required fields
          </e-text>
        </div>
      )}
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button disabled={false} action="nextStep">
          Next
        </e-button>
      </div>

      {/* Modal for custom respondent detail */}
      <p-modal
        is-open={this.isCustomDetailModalOpen}
        modal-title={this.isEditMode ? 'Edit Respondent Detail' : 'Create Respondent Detail'}
      >
        {this.isCustomDetailModalOpen && (
          <p-respondent-detail-form
            editing-detail={this.editingDetail ? JSON.stringify(this.editingDetail) : ''}
            is-edit-mode={this.isEditMode}
          ></p-respondent-detail-form>
        )}
      </p-modal>
    </article>
  );

  SurveyThankYouMessageStep: FunctionalComponent = () => (
    <article>
      <e-text>
        <strong>
          Thank You Message <span class="mandatory"> * </span>
        </strong>
      </e-text>
      <l-spacer value={0.5}></l-spacer>
      <e-text variant="footnote">
        This message will be shown to respondents after they submit their response
      </e-text>
      <l-spacer value={1}></l-spacer>
      <e-input
        type="text"
        name="surveyThankYouMessage"
        placeholder="e.g. Thank you for your feedback! We appreciate your time."
        value={this.surveyThankYouMessage}
      ></e-input>
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button disabled={this.surveyThankYouMessage.trim().length === 0} action="nextStep">
          Next
        </e-button>
      </div>
    </article>
  );

  SurveyPreviewStep: FunctionalComponent = () => (
    <article>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.surveyTitle.length > 0 ? false : true}
          action="createSurvey"
          active={this.isCreatingSurvey}
        >
          Confirm & Create
        </e-button>
      </div>
      <l-spacer value={2}></l-spacer>
      <c-card>
        <l-row justifyContent="flex-start">
          <e-image src="../../../assets/icon/light/info-light.svg" width="2em"></e-image>
          <l-spacer variant="horizontal" value={0.5}></l-spacer>
          <e-text variant="heading">Basic Info</e-text>
        </l-row>{' '}
        <l-spacer value={1}></l-spacer>
        <l-separator></l-separator>
        <l-spacer value={2}></l-spacer>
        <e-text variant="footnote">SURVEY TYPE</e-text>
        <e-text>{this.generateSurveyName(this.surveyType)}</e-text>
        <l-spacer value={2}></l-spacer>
        <e-text variant="footnote">SURVEY TITLE</e-text>
        <e-text>{this.surveyTitle}</e-text>
        <l-spacer value={2}></l-spacer>
        <e-text variant="footnote">DISTRIBUTION MODE</e-text>
        <e-text>
          {this.surveyDistributionMode === 'embed' && 'Embed survey into website or app'}
          {this.surveyDistributionMode === 'link' && 'Share survey as a link'}
          {this.surveyDistributionMode === 'embed&link' && 'Both, embed survey and share as a link'}
        </e-text>
        {this.surveyDistributionMode != 'link' && (
          <div>
            <l-spacer value={2}></l-spacer>
            <e-text variant="footnote">SURVEY EMBED LINK</e-text>
            <e-link variant="externalLink" url={this.surveyEmbedUrl}>
              {this.surveyEmbedUrl}
            </e-link>
          </div>
        )}
      </c-card>
      <l-spacer value={2}></l-spacer>

      {this.surveyType === 'sensePoll' && <this.SensePollPreview></this.SensePollPreview>}
      {this.surveyType === 'senseQuery' && <this.SenseQueryPreview></this.SenseQueryPreview>}
      {this.surveyType === 'sensePriority' && (
        <this.SensePriorityPreview></this.SensePriorityPreview>
      )}
      {this.surveyRespondentDetails.length > 0 && (
        <div>
          {' '}
          <l-spacer value={2}></l-spacer>
          <c-card>
            <l-row justifyContent="flex-start">
              <e-image src="../../../assets/icon/light/users-light.svg" width="2em"></e-image>
              <l-spacer variant="horizontal" value={0.5}></l-spacer>
              <e-text variant="heading">Respondent Details</e-text>
            </l-row>{' '}
            <l-spacer value={1}></l-spacer>
            <l-separator></l-separator>
            <l-spacer value={2}></l-spacer>
            <e-text variant="footnote">DETAILS REQUESTED FROM RESPONDENTS</e-text>
            <l-spacer value={0.25}></l-spacer>
            <ul class="survey-preview-options-list">
              {this.surveyRespondentDetails.map((detail: any) => (
                <li>
                  {detail.label}
                  {detail.required && <span class="required-badge"> *</span>}
                  {detail.inputType === 'text' && <span class="detail-options"> - Text input</span>}
                  {detail.inputType === 'email' && (
                    <span class="detail-options"> - Email input</span>
                  )}
                  {detail.inputType === 'number' && (
                    <span class="detail-options"> - Number input</span>
                  )}
                  {detail.inputType === 'select' && detail.options && (
                    <span class="detail-options">
                      {' '}
                      - Dropdown with {detail.options.length} options
                    </span>
                  )}
                  {detail.inputType === 'radio' && detail.options && (
                    <span class="detail-options">
                      {' '}
                      - Radio buttons with {detail.options.length} options
                    </span>
                  )}
                  {detail.inputType === 'checkbox' && detail.options && (
                    <span class="detail-options">
                      {' '}
                      - Checkboxes with {detail.options.length} options
                    </span>
                  )}
                </li>
              ))}
            </ul>
            <l-spacer value={1}></l-spacer>
            <e-text variant="footnote">
              <span class="required-badge"> * </span> are required fields
            </e-text>
          </c-card>
        </div>
      )}
      <l-spacer value={3}></l-spacer>
      <div class="row">
        <e-button variant="light" action="prevStep">
          Back
        </e-button>
        <e-button
          disabled={this.surveyTitle.length > 0 ? false : true}
          action="createSurvey"
          active={this.isCreatingSurvey}
        >
          Confirm & Create
        </e-button>
      </div>
    </article>
  );

  render() {
    return (
      <Host>
        <main>
          <l-row>
            <e-text variant="display">
              {this.surveyType.length === 0
                ? 'Create Survey'
                : `Create ${this.generateSurveyName(this.surveyType)}`}
            </e-text>
            <e-button action="closeWizard" variant="light">
              <e-image src="../../../assets/icon/red/x-red.svg" width="1.25em"></e-image>
            </e-button>
          </l-row>
          <e-text variant="footnote">
            STEP {this.getDisplayStepNumber()} OF {this.getDisplayTotalSteps()} -{' '}
            {this.currentStepIndex === 2 || this.currentStepIndex === 3
              ? `${this.generateSurveyName(this.surveyType).toUpperCase()} ${this.surveySteps[
                  this.currentStepIndex
                ].label.toUpperCase()}`
              : this.surveySteps[this.currentStepIndex].label.toUpperCase()}
          </e-text>
          <l-spacer value={1.5}></l-spacer>
          <p-dotgrid width="100%" height="50px"></p-dotgrid>
          <l-spacer value={1.5}></l-spacer>
          {this.currentStepIndex === 0 && <this.SurveyTypeStep></this.SurveyTypeStep>}
          {this.currentStepIndex === 1 && <this.SurveyBasicsStep></this.SurveyBasicsStep>}
          {this.currentStepIndex === 2 && <this.SurveyConfigPart1Step></this.SurveyConfigPart1Step>}
          {this.currentStepIndex === 3 && <this.SurveyConfigPart2Step></this.SurveyConfigPart2Step>}
          {this.currentStepIndex === 4 && (
            <this.SurveyRespondentDetailsStep></this.SurveyRespondentDetailsStep>
          )}
          {this.currentStepIndex === 5 && (
            <this.SurveyThankYouMessageStep></this.SurveyThankYouMessageStep>
          )}
          {this.currentStepIndex === 6 && <this.SurveyPreviewStep></this.SurveyPreviewStep>}
        </main>
      </Host>
    );
  }
}
