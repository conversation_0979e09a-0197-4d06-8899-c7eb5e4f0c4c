import { Component, Listen, Host, State, h } from '@stencil/core';
import { Store } from '../../../../global/script/store';

@Component({
  tag: 'p-user-control',
  styleUrl: 'p-user-control.css',
  shadow: true,
})
export class PUserControl {
  @Listen('buttonClickEvent') async handleButtonClickEvent(e) {
    if (e.detail.action === 'toggleUserControl') {
      this.isExpanded = !this.isExpanded;
    }
  }

  @State() isExpanded: boolean = false;

  render() {
    return (
      <Host>
        <e-button variant="link" action="toggleUserControl">
          <l-row align="center">
            <e-text weight="700">{Store.accountName.split(' ')[0]}</e-text>
            &nbsp;
            {this.isExpanded ? (
              <e-image src="../../../assets/icon/blue/caret-up-blue.svg" width="1em"></e-image>
            ) : (
              <e-image src="../../../assets/icon/blue/caret-down-blue.svg" width="1em"></e-image>
            )}
          </l-row>
        </e-button>
        {this.isExpanded && (
          <c-card>
            <e-list>
              <e-list-item>
                <e-link url="/">
                  <l-row justifyContent="flex-start">
                    <e-image src="../../../assets/icon/blue/house-blue.svg" width="1em"></e-image>
                    <l-spacer variant="horizontal" value={0.5}></l-spacer>
                    <e-text>Home</e-text>
                  </l-row>
                </e-link>
              </e-list-item>
              <l-spacer value={0.5}></l-spacer>
              <e-list-item>
                <e-link url="/account">
                  <l-row justifyContent="flex-start">
                    <e-image src="../../../assets/icon/blue/user-blue.svg" width="1em"></e-image>
                    <l-spacer variant="horizontal" value={0.5}></l-spacer>
                    <e-text>Account</e-text>
                  </l-row>
                </e-link>
              </e-list-item>
              <l-spacer value={0.5}></l-spacer>
              <e-list-item>
                <e-link url="/billing">
                  <l-row justifyContent="flex-start">
                    <e-image src="../../../assets/icon/blue/money-blue.svg" width="1em"></e-image>
                    <l-spacer variant="horizontal" value={0.5}></l-spacer>
                    <e-text>Billing</e-text>
                  </l-row>
                </e-link>
              </e-list-item>
              <l-spacer value={0.5}></l-spacer>
              <e-list-item>
                <e-link url="/support">
                  <l-row justifyContent="flex-start">
                    <e-image src="../../../assets/icon/blue/headset-blue.svg" width="1em"></e-image>
                    <l-spacer variant="horizontal" value={0.5}></l-spacer>
                    <e-text>Support</e-text>
                  </l-row>
                </e-link>
              </e-list-item>
              <l-spacer value={0.75}></l-spacer>
              <l-separator></l-separator>
              <l-spacer value={0.75}></l-spacer>
              <e-list-item>
                <l-row justifyContent="flex-start">
                  <e-image src="../../../assets/icon/blue/logout-blue.svg" width="1em"></e-image>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-button variant="link" action="logout">
                    <e-text>Logout</e-text>
                  </e-button>
                </l-row>
              </e-list-item>
            </e-list>
          </c-card>
        )}
      </Host>
    );
  }
}
