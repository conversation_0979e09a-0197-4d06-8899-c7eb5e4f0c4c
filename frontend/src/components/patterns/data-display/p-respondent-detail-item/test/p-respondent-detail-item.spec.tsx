import { newSpecPage } from '@stencil/core/testing';
import { PRespondentDetailItem } from '../p-respondent-detail-item';

describe('p-respondent-detail-item', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [PRespondentDetailItem],
      html: `<p-respondent-detail-item></p-respondent-detail-item>`,
    });
    expect(page.root).toEqualHtml(`
      <p-respondent-detail-item>
        <mock:shadow-root>
        </mock:shadow-root>
      </p-respondent-detail-item>
    `);
  });
});
