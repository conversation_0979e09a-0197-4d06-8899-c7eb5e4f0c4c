import { Component, Event, EventEmitter, Listen, Prop, State, Watch, h } from '@stencil/core';

@Component({
  tag: 'p-list-with-delete',
  styleUrl: 'p-list-with-delete.css',
  shadow: true,
})
export class PListWithDelete {
  @Watch('items') itemsPropWatcher(newVal: any, oldVal: any) {
    if (newVal != oldVal) {
      this.itemsList = JSON.parse(newVal);
      this.itemsList = [...this.itemsList];
    }
  }

  @Event({
    eventName: 'listWithDeleteEvent',
    bubbles: true,
  })
  listWithDeleteEventEmitter: EventEmitter;

  @Listen('buttonClickEvent') async handleButtonClickEvent(e) {
    if (e.detail.action === 'deleteListItem') {
      this.listWithDeleteEventEmitter.emit({
        name: this.name,
        value: e.detail.value,
      });
    }
  }

  @Prop() items: string;
  @Prop() name: string;
  @Prop() emptyMessage: string;

  @State() itemsList: any = [];

  componentWillLoad() {
    if (this.items.length > 0) {
      this.itemsList = JSON.parse(this.items);
      this.itemsList = [...this.itemsList];
    }
  }

  render() {
    if (this.itemsList.length > 0) {
      return (
        <ul>
          {this.itemsList.map((item: any) => (
            <li>
              <e-text>{item.label}</e-text>
              <e-button action="deleteListItem" variant="link" value={item.value}>
                <e-image src="../../../assets/icon/red/trash-red.svg" width="1em"></e-image>
              </e-button>
            </li>
          ))}
        </ul>
      );
    } else {
      return <e-text variant="footnote">{this.emptyMessage}</e-text>;
    }
  }
}
