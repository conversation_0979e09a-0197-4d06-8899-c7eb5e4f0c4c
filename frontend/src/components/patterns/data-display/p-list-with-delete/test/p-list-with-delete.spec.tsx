import { newSpecPage } from '@stencil/core/testing';
import { PListWithDelete } from '../p-list-with-delete';

describe('p-list-with-delete', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [PListWithDelete],
      html: `<p-list-with-delete></p-list-with-delete>`,
    });
    expect(page.root).toEqualHtml(`
      <p-list-with-delete>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </p-list-with-delete>
    `);
  });
});
