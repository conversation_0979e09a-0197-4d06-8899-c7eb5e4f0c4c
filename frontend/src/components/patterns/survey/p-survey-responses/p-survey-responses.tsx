import { Component, h, Prop } from '@stencil/core';

@Component({
  tag: 'p-survey-responses',
  styleUrl: 'p-survey-responses.css',
  shadow: true,
})
export class PSurveyResponses {
  @Prop() surveyId: string;
  @Prop() survey: any;

  render() {
    return (
      <div>
        <e-text variant="heading">Responses</e-text>
        <l-spacer value={1}></l-spacer>
        <c-card>
          <div class="responses-table-placeholder">
            <e-text variant="footnote">No responses yet</e-text>
            <l-spacer value={0.5}></l-spacer>
            <e-text>
              Individual response data will appear here once participants start submitting
              responses.
            </e-text>
            <l-spacer value={1}></l-spacer>
            <e-text variant="footnote">Features will include:</e-text>
            <ul>
              <li>
                <e-text variant="footnote">Response timestamps</e-text>
              </li>
              <li>
                <e-text variant="footnote">Participant details (if collected)</e-text>
              </li>
              <li>
                <e-text variant="footnote">Individual answer data</e-text>
              </li>
              <li>
                <e-text variant="footnote">Response completion status</e-text>
              </li>
            </ul>
          </div>
        </c-card>
      </div>
    );
  }
}
