import { Component, h, Prop } from '@stencil/core';

@Component({
  tag: 'p-survey-analytics',
  styleUrl: 'p-survey-analytics.css',
  shadow: true,
})
export class PSurveyAnalytics {
  @Prop() surveyId: string;
  @Prop() survey: any;

  generateSurveyPill(value: string) {
    if (value === 'sensePrice') {
      return <e-pill color="purple">SensePrice</e-pill>;
    } else if (value === 'senseChoice') {
      return <e-pill color="blue">SenseChoice</e-pill>;
    } else if (value === 'sensePoll') {
      return <e-pill color="indigo">SensePoll</e-pill>;
    } else if (value === 'senseQuery') {
      return <e-pill color="turquoise">SenseQuery</e-pill>;
    } else if (value === 'sensePriority') {
      return <e-pill color="teal">SensePriority</e-pill>;
    }
  }

  render() {
    return (
      <div>
        <e-text variant="heading">Analytics</e-text>
        <l-spacer value={1}></l-spacer>
        <c-card>
          <l-spacer value={1}></l-spacer>
          <e-text>
            Charts and graphs showing response patterns over time will be displayed here.
          </e-text>
          <l-spacer value={1}></l-spacer>
          <div
            style={{
              height: '200px',
              backgroundColor: 'var(--color__grey--50)',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <e-text variant="footnote">Analytics visualization placeholder</e-text>
          </div>
          <l-spacer value={1}></l-spacer>
          <e-text variant="footnote">
            Analytics will show response trends, completion rates over time, and demographic
            breakdowns once you have survey responses.
          </e-text>
        </c-card>
      </div>
    );
  }
}
