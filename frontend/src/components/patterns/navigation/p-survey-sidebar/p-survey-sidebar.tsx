import { Component, h, Prop, Event, EventEmitter } from '@stencil/core';

@Component({
  tag: 'p-survey-sidebar',
  styleUrl: 'p-survey-sidebar.css',
  shadow: true,
})
export class PSurveySidebar {
  @Prop() surveyId: string;
  @Prop() activePage: 'overview' | 'analytics' | 'responses' | 'export';
  @Prop() useClickHandlers: boolean = false;

  @Event() sectionChange: EventEmitter<'overview' | 'analytics' | 'responses' | 'export'>;

  handleSectionClick = (section: 'overview' | 'analytics' | 'responses' | 'export') => {
    if (this.useClickHandlers) {
      this.sectionChange.emit(section);
    }
  };

  render() {
    return (
      <div id="sidebar">
        <div class="sidebar-nav">
          <div
            class={`nav-item ${this.activePage === 'overview' ? 'nav-item--active' : ''}`}
            onClick={() => this.handleSectionClick('overview')}
          >
            {this.useClickHandlers || this.activePage === 'overview' ? (
              <l-row justifyContent="flex-start" align="center">
                <e-image src="../../../../assets/icon/blue/overview-blue.svg" width="1em"></e-image>
                <l-spacer variant="horizontal" value={0.5}></l-spacer>
                <e-text>Overview</e-text>
              </l-row>
            ) : (
              <e-link url={`/surveys/${this.surveyId}/overview`}>
                <l-row justifyContent="flex-start" align="center">
                  <e-image src="../../../../assets/icon/blue/overview-blue.svg" width="1em"></e-image>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text>Overview</e-text>
                </l-row>
              </e-link>
            )}
          </div>
          <div
            class={`nav-item ${this.activePage === 'analytics' ? 'nav-item--active' : ''}`}
            onClick={() => this.handleSectionClick('analytics')}
          >
            {this.useClickHandlers || this.activePage === 'analytics' ? (
              <l-row justifyContent="flex-start" align="center">
                <e-image src="../../../../assets/icon/blue/analytics-blue.svg" width="1em"></e-image>
                <l-spacer variant="horizontal" value={0.5}></l-spacer>
                <e-text>Analytics</e-text>
              </l-row>
            ) : (
              <e-link url={`/surveys/${this.surveyId}/analytics`}>
                <l-row justifyContent="flex-start" align="center">
                  <e-image src="../../../../assets/icon/blue/analytics-blue.svg" width="1em"></e-image>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text>Analytics</e-text>
                </l-row>
              </e-link>
            )}
          </div>
          <div
            class={`nav-item ${this.activePage === 'responses' ? 'nav-item--active' : ''}`}
            onClick={() => this.handleSectionClick('responses')}
          >
            {this.useClickHandlers || this.activePage === 'responses' ? (
              <l-row justifyContent="flex-start" align="center">
                <e-image src="../../../../assets/icon/blue/responses-blue.svg" width="1em"></e-image>
                <l-spacer variant="horizontal" value={0.5}></l-spacer>
                <e-text>Responses</e-text>
              </l-row>
            ) : (
              <e-link url={`/surveys/${this.surveyId}/responses`}>
                <l-row justifyContent="flex-start" align="center">
                  <e-image src="../../../../assets/icon/blue/responses-blue.svg" width="1em"></e-image>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text>Responses</e-text>
                </l-row>
              </e-link>
            )}
          </div>
          <div
            class={`nav-item ${this.activePage === 'export' ? 'nav-item--active' : ''}`}
            onClick={() => this.handleSectionClick('export')}
          >
            {this.useClickHandlers || this.activePage === 'export' ? (
              <l-row justifyContent="flex-start" align="center">
                <e-image src="../../../../assets/icon/blue/export-blue.svg" width="1em"></e-image>
                <l-spacer variant="horizontal" value={0.5}></l-spacer>
                <e-text>Export</e-text>
              </l-row>
            ) : (
              <e-link url={`/surveys/${this.surveyId}/export`}>
                <l-row justifyContent="flex-start" align="center">
                  <e-image src="../../../../assets/icon/blue/export-blue.svg" width="1em"></e-image>
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                  <e-text>Export</e-text>
                </l-row>
              </e-link>
            )}
          </div>
        </div>
      </div>
    );
  }
}
